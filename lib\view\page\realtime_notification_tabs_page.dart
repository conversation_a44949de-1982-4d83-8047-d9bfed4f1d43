import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/realtime_notification_controller.dart';
import '../../controllers/realtime_bug_report_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/realtime_notification_model.dart';
import '../../models/realtime_bug_report_model.dart';
import 'realtime_notification_detail_page.dart';
import 'realtime_bug_report_detail_page.dart';
import 'new_notification_page.dart';
import 'new_bug_report_page.dart';

class RealtimeNotificationTabsPage extends StatefulWidget {
  const RealtimeNotificationTabsPage({Key? key}) : super(key: key);

  @override
  State<RealtimeNotificationTabsPage> createState() =>
      _RealtimeNotificationTabsPageState();
}

// Đổi tên file thành BugIssuesPage trong lần cập nhật tiếp theo

class _RealtimeNotificationTabsPageState
    extends State<RealtimeNotificationTabsPage> {
  final RealtimeNotificationController _notificationController =
      Get.find<RealtimeNotificationController>();
  final RealtimeBugReportController _bugReportController =
      Get.find<RealtimeBugReportController>();
  final AuthController _authController = Get.find<AuthController>();

  @override
  void initState() {
    super.initState();
    // Khi trang được mở, tải cả thông báo và báo cáo lỗi
    _notificationController.refreshNotifications();
    _bugReportController.refreshBugReports();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Báo cáo lỗi',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _notificationController.refreshNotifications();
              _bugReportController.refreshBugReports();
            },
          ),
          // Nút làm mới
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // Hiển thị dialog lọc báo cáo lỗi
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(
                    'Lọc báo cáo lỗi',
                    style: GoogleFonts.mulish(fontWeight: FontWeight.bold),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        title: const Text('Tất cả'),
                        onTap: () {
                          _bugReportController.refreshBugReports();
                          Navigator.pop(context);
                        },
                      ),
                      ListTile(
                        title: const Text('Chưa nhận'),
                        onTap: () {
                          // Thêm logic lọc theo trạng thái 'pending'
                          Navigator.pop(context);
                        },
                      ),
                      ListTile(
                        title: const Text('Đang fix'),
                        onTap: () {
                          // Thêm logic lọc theo trạng thái 'inProgress'
                          Navigator.pop(context);
                        },
                      ),
                      ListTile(
                        title: const Text('Đã fix'),
                        onTap: () {
                          // Thêm logic lọc theo trạng thái 'fixed'
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Nút tạo báo cáo lỗi mới
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Get.to(() => const NewBugReportPage());
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('Báo cáo lỗi mới'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Nội dung chính
            Expanded(
              child: _buildBugReportsTab(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBugReportsTab() {
    return Obx(() {
      final bugReports = _authController.isAdmin
          ? _bugReportController.allBugReports
          : _bugReportController.userBugReports;

      if (_bugReportController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_bugReportController.errorMessage.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _bugReportController.errorMessage,
                style: GoogleFonts.mulish(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _bugReportController.refreshBugReports(),
                child: const Text('Thử lại'),
              ),
            ],
          ),
        );
      }

      if (bugReports.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Không có báo cáo lỗi nào',
                style: GoogleFonts.mulish(),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.to(() => const NewBugReportPage()),
                child: const Text('Báo cáo lỗi mới'),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () async {
          _bugReportController.refreshBugReports();
        },
        child: ListView.builder(
          itemCount: bugReports.length,
          itemBuilder: (context, index) {
            final bugReport = bugReports[index];
            return _buildBugReportItem(bugReport);
          },
        ),
      );
    });
  }

  Widget _buildBugReportItem(RealtimeBugReportModel bugReport) {
    final statusColor = _getStatusColor(bugReport.status);
    final statusName = _getStatusName(bugReport.status);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Get.to(() => RealtimeBugReportDetailPage(bugReportId: bugReport.id));
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      bugReport.title,
                      style: GoogleFonts.mulish(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor),
                    ),
                    child: Text(
                      statusName,
                      style: GoogleFonts.mulish(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                bugReport.description,
                style: GoogleFonts.mulish(
                  color: Colors.grey[300],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Báo cáo bởi: ${bugReport.reportedByName}',
                    style: GoogleFonts.mulish(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    _formatDate(DateTime.fromMillisecondsSinceEpoch(
                        bugReport.createdAt)),
                    style: GoogleFonts.mulish(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'accepted':
        return Colors.blue;
      case 'inProgress':
        return Colors.orange;
      case 'fixed':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getStatusName(String status) {
    switch (status) {
      case 'accepted':
        return 'Đã nhận';
      case 'inProgress':
        return 'Đang fix';
      case 'fixed':
        return 'Đã fix';
      default:
        return 'Chưa nhận';
    }
  }
}
