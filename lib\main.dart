import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'controllers/auth_controller.dart';
import 'controllers/movie_controller.dart';
import 'controllers/favorite_controller.dart';
import 'controllers/ticket_controller.dart';
import 'controllers/language_controller.dart';
import 'controllers/banner_controller.dart';
import 'controllers/notification_controller.dart';
import 'controllers/bug_report_controller.dart';
import 'controllers/realtime_notification_controller.dart';
import 'controllers/realtime_bug_report_controller.dart';
import 'bindings/realtime_database_binding.dart';
import 'translations/app_translations.dart';
import 'utils/storage_cleanup.dart';
import 'utils/developer_mode.dart';
import 'view/page/auth/login_page.dart';
import 'view/page/splash_screen.dart';
import 'view/page/profile_edit_page.dart';
import 'view/page/bug_report_detail_page.dart';
import 'view/page/realtime_notification_tabs_page.dart';
import 'view/page/realtime_bug_report_detail_page.dart';
import 'view/root_page.dart';
import 'view/admin/admin_routes.dart';
import 'debug_firestore.dart';
import 'debug_add_banner.dart';

Future<void> _forceResetAllData() async {
  await StorageCleanup.clearAllData();

  await StorageCleanup.checkAndFixProblematicData();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await _forceResetAllData();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize controllers
  Get.put(AuthController());
  Get.put(MovieController());
  Get.put(FavoriteController());
  Get.put(TicketController());
  Get.put(LanguageController());
  Get.put(BannerController());
  Get.put(NotificationController());
  Get.put(BugReportController());

  // Initialize Realtime Database controllers
  Get.put(RealtimeNotificationController());
  Get.put(RealtimeBugReportController());

  // Initialize developer mode
  Get.put(DeveloperMode());

  // Initialize bindings
  RealtimeDatabaseBinding().dependencies();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();

    return GetMaterialApp(
      title: 'Đớp Phim',
      debugShowCheckedModeBanner: false,

      // Translations
      translations: AppTranslations(),
      locale: languageController.currentLocale,
      fallbackLocale: const Locale('en', 'US'),

      // Routes
      getPages: [
        ...AdminRoutes.routes,
        GetPage(
          name: '/debug',
          page: () => const FirestoreDebugPage(),
        ),
        GetPage(
          name: '/debug/add-banner',
          page: () => const AddBannerDebugPage(),
        ),
        GetPage(
          name: '/profile/edit',
          page: () => const ProfileEditPage(),
        ),
        GetPage(
          name: '/bug_report_detail',
          page: () {
            // Kiểm tra xem có tham số bugReportId không
            final bugReportId = Get.parameters['bugReportId'];

            // Nếu có tham số bugReportId và có tiền tố 'rt_', sử dụng trang Realtime
            if (bugReportId != null && bugReportId.startsWith('rt_')) {
              final realBugReportId =
                  bugReportId.substring(3); // Bỏ tiền tố 'rt_'
              return RealtimeBugReportDetailPage(bugReportId: realBugReportId);
            }

            // Ngược lại, sử dụng trang thông thường
            return const BugReportDetailPage();
          },
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_notifications',
          page: () => const RealtimeNotificationTabsPage(),
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_bug_report_detail/:bugReportId',
          page: () => RealtimeBugReportDetailPage(
            bugReportId: Get.parameters['bugReportId'] ?? '',
          ),
          binding: RealtimeDatabaseBinding(),
        ),
      ],
      initialRoute: '/',

      theme: ThemeData(
        primarySwatch: Colors.blue,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: const Color(0xff121212),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
      ),
      home: const AuthCheckPage(),
    );
  }
}

class AuthCheckPage extends StatefulWidget {
  const AuthCheckPage({Key? key}) : super(key: key);

  @override
  State<AuthCheckPage> createState() => _AuthCheckPageState();
}

class _AuthCheckPageState extends State<AuthCheckPage> {
  final bool _showSplash = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();

    if (_showSplash) {
      return const SplashScreen(
        nextScreen: LoginPage(),
      );
    }

    return _buildAuthScreen(authController);
  }

  Widget _buildAuthScreen(AuthController authController) {
    return Obx(() {
      if (authController.isLoading) {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      } else if (authController.isLoggedIn) {
        return RootPage(i: 0);
      } else {
        return const LoginPage();
      }
    });
  }
}
