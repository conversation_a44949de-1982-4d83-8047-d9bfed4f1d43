import 'dart:async';
import 'dart:developer' as developer;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/movie_controller.dart';
import '../../controllers/banner_controller.dart';
import '../../models/banner_model.dart';
import 'movie_detail_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  final BannerController _bannerController = Get.find<BannerController>();
  final MovieController _movieController = Get.find<MovieController>();

  // Selected filter for movie rankings
  final RxString _selectedFilter = 'Hôm nay'.obs;

  // Auto-scrolling controller
  late final ScrollController _genreScrollController;
  Timer? _autoScrollTimer;

  final List<Map<String, dynamic>> menuList = [
    {
      'icon': Icons.local_fire_department,
      'title': 'Hành động',
    },
    {
      'icon': Icons.sentiment_satisfied_alt,
      'title': 'Hài',
    },
    {
      'icon': Icons.favorite,
      'title': 'Tình cảm',
    },
    {
      'icon': Icons.psychology,
      'title': 'Kinh dị',
    },
    {
      'icon': Icons.auto_awesome,
      'title': 'Viễn tưởng',
    },
    {
      'icon': Icons.family_restroom,
      'title': 'Gia đình',
    },
  ];

  @override
  void initState() {
    super.initState();
    developer.log('HomePage: Initializing and fetching home banners',
        name: 'HomePage');

    // Clear any previous errors
    _bannerController.errorMessage.value = '';

    // Fetch banners with a slight delay to ensure Firebase is fully initialized
    Future.delayed(const Duration(milliseconds: 500), () {
      _bannerController.fetchActiveBanners(type: BannerType.home);
    });

    // Add a listener to refresh banners if they're empty
    ever(_bannerController.homeBanners, (List<BannerModel> banners) {
      if (banners.isEmpty && !_bannerController.isLoading.value && mounted) {
        developer.log('HomePage: Banners empty, retrying fetch after delay',
            name: 'HomePage');
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted && _bannerController.homeBanners.isEmpty) {
            _bannerController.fetchActiveBanners(type: BannerType.home);
          }
        });
      }
    });

    // Initialize auto-scrolling for genre list
    _genreScrollController = ScrollController();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _genreScrollController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    const duration = Duration(seconds: 15);
    _autoScrollTimer = Timer.periodic(duration, (timer) {
      if (_genreScrollController.hasClients) {
        final currentPosition = _genreScrollController.offset;
        final maxScrollExtent = _genreScrollController.position.maxScrollExtent;

        if (currentPosition >= maxScrollExtent) {
          // If we're at the end, scroll back to the beginning
          _genreScrollController.animateTo(
            0,
            duration: const Duration(seconds: 1),
            curve: Curves.easeInOut,
          );
        } else {
          // Otherwise, scroll forward
          _genreScrollController.animateTo(
            currentPosition + 200, // Scroll by 200 pixels
            duration: const Duration(seconds: 1),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }

  Widget _buildAutoScrollingGenreList() {
    // Create a list with double the items for infinite scrolling effect
    final extendedList = [...menuList, ...menuList];

    return ListView.builder(
      controller: _genreScrollController,
      scrollDirection: Axis.horizontal,
      itemCount: extendedList.length,
      itemBuilder: (context, index) {
        final item = extendedList[index];
        return MenuBtns(
          title: item['title'],
          icon: item['icon'] as IconData,
        );
      },
    );
  }

  // Build filter chip for movie rankings
  Widget _buildFilterChip(String label, bool isSelected) {
    return Obx(() {
      final isActive = _selectedFilter.value == label;
      return GestureDetector(
        onTap: () {
          _selectedFilter.value = label;
        },
        child: Container(
          margin: const EdgeInsets.only(right: 10),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            label,
            style: TextStyle(
              color: isActive ? Colors.white : Colors.white70,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final searchTextController = TextEditingController();
    return SingleChildScrollView(
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // top screen
            Padding(
              padding: const EdgeInsets.only(left: 25, right: 25, top: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Wellcome note
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: '${'hello'.tr},',
                              style: GoogleFonts.mulish(
                                textStyle: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            TextSpan(
                              text: ' Daizy',
                              style: GoogleFonts.mulish(
                                textStyle: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.notifications_outlined,
                            color: Colors.white,
                          ))
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  // Search Box
                  Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0x1e6b66a6),
                          Color(0x1e75d1dd),
                        ],
                      ),
                    ),
                    child: TextField(
                      style: GoogleFonts.mulish(
                        textStyle: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                      controller: searchTextController,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(
                          Icons.search_outlined,
                          color: Colors.white54,
                          size: 32,
                        ),
                        suffixIcon: IconButton(
                          onPressed: () {},
                          icon: const Icon(
                            Icons.arrow_forward,
                            color: Colors.white60,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.transparent,
                        enabledBorder: const OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Colors.white38,
                            width: 0.0,
                          ),
                        ),
                        label: Text(
                          'search_hint'.tr,
                          style: GoogleFonts.mulish(
                            textStyle: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w100,
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  // Popular Section
                  Text(
                    'popular_movies'.tr,
                    style: GoogleFonts.mulish(
                      textStyle: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Obx(() {
              final homeBanners = _bannerController.homeBanners;
              final errorMsg = _bannerController.errorMessage.value;

              // Debug information
              developer.log(
                  'Banner status - Loading: ${_bannerController.isLoading.value}, '
                  'Banners count: ${homeBanners.length}, '
                  'Error: ${errorMsg.isNotEmpty ? errorMsg : "None"}',
                  name: 'HomePage');

              if (_bannerController.isLoading.value) {
                return const SizedBox(
                  height: 170,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (homeBanners.isEmpty) {
                return SizedBox(
                  height: 170,
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'No banners available',
                          style: TextStyle(color: Colors.white70),
                        ),
                        if (errorMsg.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              'Error: $errorMsg',
                              style: const TextStyle(
                                  color: Colors.red, fontSize: 12),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }

              return CarouselSlider.builder(
                options: CarouselOptions(
                  height: 170,
                  autoPlay: true,
                  enlargeCenterPage: true,
                  autoPlayInterval: const Duration(
                    seconds: 7,
                  ),
                  autoPlayAnimationDuration: const Duration(
                    seconds: 1,
                  ),
                ),
                itemCount: homeBanners.length,
                itemBuilder: (_, index, realIndex) {
                  final banner = homeBanners[index];
                  return InkWell(
                    onTap: () {
                      // Use the first movie from popular movies or a default ID
                      final movieController = Get.find<MovieController>();
                      final movieId = movieController.popularMovies.isNotEmpty
                          ? movieController
                              .popularMovies[
                                  index % movieController.popularMovies.length]
                              .id
                          : 5; // Default to Thor: The Dark World
                      Get.to(() => MovieDetailsPage(movieId: movieId));
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          fit: BoxFit.fill,
                          image: NetworkImage(banner.imageUrl),
                        ),
                        borderRadius: BorderRadius.circular(25),
                        gradient: const LinearGradient(
                          colors: [
                            Color(0x00121212),
                            Color(0xff121212),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),
            const SizedBox(
              height: 20,
            ),
            // Movie Rankings Filter
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Xếp hạng phim',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      TextButton(
                        onPressed: () {},
                        child: Text(
                          'see_all'.tr,
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip('Hôm nay', true),
                        _buildFilterChip('Tuần này', false),
                        _buildFilterChip('Tháng này', false),
                        _buildFilterChip('Mùa hè', false),
                        _buildFilterChip('Mùa thu', false),
                        _buildFilterChip('Mùa đông', false),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Featured Section - Auto Scrolling Genre List
            SizedBox(
              height: 100,
              child: _buildAutoScrollingGenreList(),
            ),
            // Filtered Movies by Ranking
            const SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25),
              child: Obx(() => Text(
                    'Phim ${_selectedFilter.value}',
                    style: GoogleFonts.mulish(
                      textStyle: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  )),
            ),
            const SizedBox(
              height: 20,
            ),
            Obx(() {
              // Use popular movies as the data source for filtered movies
              final filteredMovies = _movieController.popularMovies;

              if (_movieController.isLoadingPopular.value) {
                return const SizedBox(
                  height: 230,
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (filteredMovies.isEmpty) {
                return const SizedBox(
                  height: 230,
                  child: Center(
                    child: Text(
                      'Không có phim nào',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                );
              }

              return CarouselSlider.builder(
                options: CarouselOptions(
                  height: 230,
                  viewportFraction: 0.55,
                  autoPlay: true,
                  enlargeCenterPage: true,
                  autoPlayInterval: const Duration(
                    seconds: 7,
                  ),
                  autoPlayAnimationDuration: const Duration(
                    seconds: 1,
                  ),
                ),
                itemCount: filteredMovies.length,
                itemBuilder: (_, index, realIndex) {
                  final movie = filteredMovies[index];
                  return GestureDetector(
                    onTap: () {
                      Get.to(() => MovieDetailsPage(movieId: movie.id));
                    },
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            width: MediaQuery.of(context).size.width * 0.5,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                fit: BoxFit.cover,
                                image: NetworkImage(movie.fullPosterPath),
                              ),
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          movie.title,
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              );
            }),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

class MenuBtns extends StatelessWidget {
  const MenuBtns({
    required this.title,
    required this.icon,
    Key? key,
  }) : super(key: key);

  final String title;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(5),
      height: 80,
      width: 70,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0x1EA6A1E0),
            Color(0x1EA1F3FE),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 30,
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              textStyle: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w200,
              ),
            ),
          )
        ],
      ),
    );
  }
}
