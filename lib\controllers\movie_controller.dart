import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../services/movie_service.dart';

class MovieController extends GetxController {
  final MovieService _movieService = MovieService();
  
  final RxList<Movie> popularMovies = <Movie>[].obs;
  final RxList<Movie> upcomingMovies = <Movie>[].obs;
  final RxList<Movie> searchResults = <Movie>[].obs;
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);
  
  final RxBool isLoadingPopular = false.obs;
  final RxBool isLoadingUpcoming = false.obs;
  final RxBool isLoadingSearch = false.obs;
  final RxBool isLoadingMovieDetails = false.obs;
  
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchPopularMovies();
    fetchUpcomingMovies();
  }

  Future<void> fetchPopularMovies() async {
    try {
      isLoadingPopular.value = true;
      errorMessage.value = '';
      
      final movies = await _movieService.getPopularMovies();
      popularMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load popular movies: $e';
    } finally {
      isLoadingPopular.value = false;
    }
  }

  Future<void> fetchUpcomingMovies() async {
    try {
      isLoadingUpcoming.value = true;
      errorMessage.value = '';
      
      final movies = await _movieService.getUpcomingMovies();
      upcomingMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load upcoming movies: $e';
    } finally {
      isLoadingUpcoming.value = false;
    }
  }

  Future<void> searchMovies(String query) async {
    if (query.isEmpty) {
      searchResults.clear();
      return;
    }
    
    try {
      isLoadingSearch.value = true;
      errorMessage.value = '';
      searchQuery.value = query;
      
      final movies = await _movieService.searchMovies(query);
      searchResults.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to search movies: $e';
    } finally {
      isLoadingSearch.value = false;
    }
  }

  Future<Movie?> getMovieDetails(int movieId) async {
    try {
      isLoadingMovieDetails.value = true;
      errorMessage.value = '';
      
      final movie = await _movieService.getMovieDetails(movieId);
      selectedMovie.value = movie;
      return movie;
    } catch (e) {
      errorMessage.value = 'Failed to load movie details: $e';
      return null;
    } finally {
      isLoadingMovieDetails.value = false;
    }
  }

  void clearSearch() {
    searchResults.clear();
    searchQuery.value = '';
  }
}
