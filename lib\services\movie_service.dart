import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/movie_model.dart';

class MovieService {
  static const String _apiKey = '2cb4cc8e3daa936694231cd7abb9d9ef';
  static const String _baseUrl = 'https://api.themoviedb.org/3';

  final bool _useMockData = true;

  Future<List<Movie>> getPopularMovies({int page = 1}) async {
    if (_useMockData) {
      return _getMockPopularMovies();
    }

    final response = await http.get(
      Uri.parse('$_baseUrl/movie/popular?api_key=$_apiKey&page=$page'),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final results = data['results'] as List;
      return results.map((movie) => Movie.fromJson(movie)).toList();
    } else {
      throw Exception('Failed to load popular movies');
    }
  }

  // Get movie details
  Future<Movie> getMovieDetails(int movieId) async {
    if (_useMockData) {
      return _getMockMovieDetails(movieId);
    }

    final response = await http.get(
      Uri.parse(
          '$_baseUrl/movie/$movieId?api_key=$_apiKey&append_to_response=credits'),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return Movie.fromJson(data);
    } else {
      throw Exception('Failed to load movie details');
    }
  }

  // Search movies
  Future<List<Movie>> searchMovies(String query, {int page = 1}) async {
    if (_useMockData) {
      return _getMockSearchResults(query);
    }

    final response = await http.get(
      Uri.parse(
          '$_baseUrl/search/movie?api_key=$_apiKey&query=$query&page=$page'),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final results = data['results'] as List;
      return results.map((movie) => Movie.fromJson(movie)).toList();
    } else {
      throw Exception('Failed to search movies');
    }
  }

  // Get upcoming movies
  Future<List<Movie>> getUpcomingMovies({int page = 1}) async {
    if (_useMockData) {
      return _getMockUpcomingMovies();
    }

    final response = await http.get(
      Uri.parse('$_baseUrl/movie/upcoming?api_key=$_apiKey&page=$page'),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final results = data['results'] as List;
      return results.map((movie) => Movie.fromJson(movie)).toList();
    } else {
      throw Exception('Failed to load upcoming movies');
    }
  }

  // Mock data methods
  List<Movie> _getMockPopularMovies() {
    return [
      Movie(
        id: 1,
        title: 'Doctor Strange',
        subtitle: 'in the Multiverse of Madness',
        overview:
            'Doctor Strange, with the help of mystical allies both old and new, traverses the mind-bending and dangerous alternate realities of the Multiverse to confront a mysterious new adversary.',
        posterPath: '/uGBVj3bEbCoZbDjjl9wTxcygko1.jpg',
        backdropPath: '/gUNRlH66yNDH3NQblYMIwgZXJ2u.jpg',
        genres: ['Action', 'Fantasy'],
        releaseDate: '2022-05-04',
        voteAverage: 7.5,
        ageRating: 'PG-13',
      ),
      Movie(
        id: 2,
        title: 'Avengers',
        subtitle: 'Endgame',
        overview:
            'After the devastating events of Avengers: Infinity War, the universe is in ruins. With the help of remaining allies, the Avengers assemble once more in order to reverse Thanos\' actions and restore balance to the universe.',
        posterPath: '/7RyHsO4yDXtBv1zUU3mTpHeQ0d5.jpg',
        backdropPath: '/7RyHsO4yDXtBv1zUU3mTpHeQ0d5.jpg',
        genres: ['Action', 'Adventure', 'Sci-Fi'],
        releaseDate: '2019-04-26',
        voteAverage: 8.4,
        ageRating: 'PG-13',
      ),
      Movie(
        id: 3,
        title: 'Avatar',
        subtitle: 'The Way of Water',
        overview:
            'Set more than a decade after the events of the first film, "Avatar: The Way of Water" begins to tell the story of the Sully family, the trouble that follows them, the lengths they go to keep each other safe, the battles they fight to stay alive, and the tragedies they endure.',
        posterPath: '/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg',
        backdropPath: '/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg',
        genres: ['Action', 'Adventure', 'Fantasy', 'Sci-Fi'],
        releaseDate: '2022-12-14',
        voteAverage: 7.7,
        ageRating: 'PG-13',
      ),
      Movie(
        id: 4,
        title: 'KGF',
        subtitle: 'Chapter 2',
        overview:
            'The blood-soaked land of Kolar Gold Fields (KGF) has a new overlord now - Rocky, whose name strikes fear in the heart of his foes. His allies look up to Rocky as their savior, the government sees him as a threat to law and order; enemies are clamoring for revenge and conspiring for his downfall.',
        posterPath: '/jPEXVFKQY2oPUJ5VwmRVF6ESrOy.jpg',
        backdropPath: '/jPEXVFKQY2oPUJ5VwmRVF6ESrOy.jpg',
        genres: ['Action', 'Drama'],
        releaseDate: '2022-04-14',
        voteAverage: 8.3,
        ageRating: '16+',
      ),
      Movie(
        id: 5,
        title: 'Thor',
        subtitle: 'The Dark World',
        overview:
            'Thor fights to restore order across the cosmos… but an ancient race led by the vengeful Malekith returns to plunge the universe back into darkness. Faced with an enemy that even Odin and Asgard cannot withstand, Thor must embark on his most perilous and personal journey yet, one that will reunite him with Jane Foster and force him to sacrifice everything to save us all.',
        posterPath: '/wp6OxE4poJ4G7c0U2ZIXasTSMR7.jpg',
        backdropPath: '/wp6OxE4poJ4G7c0U2ZIXasTSMR7.jpg',
        genres: ['Action', 'Adventure', 'Fantasy'],
        releaseDate: '2013-10-30',
        voteAverage: 6.8,
        ageRating: 'PG-13',
      ),
    ];
  }

  List<Movie> _getMockUpcomingMovies() {
    return [
      Movie(
        id: 6,
        title: 'Deadpool & Wolverine',
        overview:
            "Wade Wilson's peaceful life is interrupted when the Wolverine from another universe asks for his help.",
        posterPath: '/4m1Au3YkjqsxF8iwQy0fPYSxE0h.jpg',
        backdropPath: '/mBaXZ95R2OxueZhvQbcEWy2DqyO.jpg',
        genres: ['Action', 'Comedy'],
        releaseDate: '2024-07-26',
        voteAverage: 8.0,
        ageRating: 'R',
      ),
      Movie(
        id: 7,
        title: 'Venom: The Last Dance',
        overview:
            "Eddie and Venom are on the run. Hunted by both of their worlds and with the net closing in, the duo are forced into a devastating decision that will bring the curtains down on Venom and Eddie's last dance.",
        posterPath: '/6WgZ7UvQ0DQzE7e8LlPC9xhFzce.jpg',
        backdropPath: '/wrPhFo9z4xO6FLRCQVDzPnOGJlq.jpg',
        genres: ['Action', 'Sci-Fi'],
        releaseDate: '2024-10-25',
        voteAverage: 7.2,
        ageRating: 'PG-13',
      ),
    ];
  }

  Movie _getMockMovieDetails(int movieId) {
    final movies = [..._getMockPopularMovies(), ..._getMockUpcomingMovies()];
    final movie = movies.firstWhere(
      (movie) => movie.id == movieId,
      orElse: () => Movie(
        id: 0,
        title: 'Movie Not Found',
      ),
    );

    // Add cast for Thor: The Dark World
    if (movieId == 5) {
      return movie.copyWith(
        cast: [
          Cast(
            id: 1,
            name: 'Chris Hemsworth',
            character: 'Thor',
            profilePath: '/jpurJ9jAcLCYjgHHfYF32m3zJYm.jpg',
          ),
          Cast(
            id: 2,
            name: 'Natalie Portman',
            character: 'Jane Foster',
            profilePath: '/edPU5HxncLWa1YkgRPNkSd68ONG.jpg',
          ),
          Cast(
            id: 3,
            name: 'Tom Hiddleston',
            character: 'Loki',
            profilePath: '/mclHxMm8aPlCPKptP67257F5GPo.jpg',
          ),
          Cast(
            id: 4,
            name: 'Anthony Hopkins',
            character: 'Odin',
            profilePath: '/jdoBTIru71FbPuHGEgox3gBDXMr.jpg',
          ),
          Cast(
            id: 5,
            name: 'Christopher Eccleston',
            character: 'Malekith',
            profilePath: '/wEy5qSDT5jT3ZASc2hbwi59voPL.jpg',
          ),
        ],
      );
    }

    return movie;
  }

  List<Movie> _getMockSearchResults(String query) {
    final allMovies = [..._getMockPopularMovies(), ..._getMockUpcomingMovies()];
    return allMovies
        .where(
            (movie) => movie.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }
}
